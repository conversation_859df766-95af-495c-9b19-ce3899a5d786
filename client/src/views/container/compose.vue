<template>
  <div class="container-compose-container page-container">
    <t-card title="Docker Compose 管理">
      <template #actions>
        <t-space>
          <t-button theme="primary" @click="handleCreateCompose">
            <template #icon>
              <t-icon name="add" />
            </template>
            创建编排
          </t-button>
          <t-button theme="default" @click="fetchComposeList">
            <template #icon>
              <t-icon name="refresh" />
            </template>
            刷新
          </t-button>
        </t-space>
      </template>

      <t-table
        :data="composeList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
        stripe
        hover
        row-key="name"
      >
        <template #status="{ row }">
          <t-tag :theme="getStatusTheme(row.status)" variant="light">
            {{ row.status }}
          </t-tag>
        </template>

        <template #created="{ row }">
          {{ formatDate(row.created) }}
        </template>

        <template #operation="{ row }">
          <t-space>
            <t-tooltip content="查看详情">
              <t-button variant="text" theme="primary" @click="handleViewCompose(row)">
                <template #icon>
                  <t-icon name="view-list" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip content="编辑">
              <t-button variant="text" theme="default" @click="handleEditCompose(row)">
                <template #icon>
                  <t-icon name="edit" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip :content="row.status === 'running' ? '停止' : '启动'">
              <t-button
                variant="text"
                :theme="row.status === 'running' ? 'danger' : 'success'"
                @click="row.status === 'running' ? handleStopCompose(row) : handleStartCompose(row)"
              >
                <template #icon>
                  <t-icon :name="row.status === 'running' ? 'stop-circle' : 'play-circle'" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip content="删除">
              <t-button variant="text" theme="danger" @click="handleRemoveCompose(row)">
                <template #icon>
                  <t-icon name="delete" />
                </template>
              </t-button>
            </t-tooltip>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 创建/编辑编排对话框 -->
    <t-dialog
      v-model:visible="composeDialogVisible"
      :header="isEdit ? '编辑编排' : '创建编排'"
      :confirm-btn="{ content: isEdit ? '更新' : '创建', theme: 'primary' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="confirmSaveCompose"
      width="800px"
      top="5vh"
    >
      <t-form ref="form" :data="formData" :rules="rules" label-width="120px">
        <t-form-item label="编排名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入编排名称" :disabled="isEdit" />
        </t-form-item>
        <t-form-item label="Docker Compose" name="compose">
          <t-textarea
            v-model="formData.compose"
            placeholder="请输入 docker-compose.yml 内容"
            :autosize="{ minRows: 10, maxRows: 20 }"
          />
        </t-form-item>
        <t-form-item label="环境变量">
          <t-space direction="vertical" style="width: 100%">
            <t-space v-for="(env, index) in formData.envs" :key="index" align="center">
              <t-input v-model="env.key" placeholder="变量名" style="width: 200px" />
              <span>=</span>
              <t-input v-model="env.value" placeholder="变量值" style="width: 300px" />
              <t-button theme="danger" variant="text" @click="removeEnv(index)">
                <template #icon>
                  <t-icon name="close" />
                </template>
              </t-button>
            </t-space>
            <t-button theme="primary" variant="text" @click="addEnv">
              <template #icon>
                <t-icon name="add" />
              </template>
              添加环境变量
            </t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 查看详情对话框 -->
    <t-dialog
      v-model:visible="viewDialogVisible"
      header="编排详情"
      :confirm-btn="{ content: '关闭', theme: 'primary' }"
      :cancel-btn="null"
      width="800px"
    >
      <t-descriptions bordered>
        <t-descriptions-item label="编排名称">{{ currentCompose.name }}</t-descriptions-item>
        <t-descriptions-item label="状态">
          <t-tag :theme="getStatusTheme(currentCompose.status)" variant="light">
            {{ currentCompose.status }}
          </t-tag>
        </t-descriptions-item>
        <t-descriptions-item label="创建时间">{{ formatDate(currentCompose.created) }}</t-descriptions-item>
      </t-descriptions>
      
      <div class="compose-content">
        <h4>Docker Compose 内容：</h4>
        <pre>{{ currentCompose.compose }}</pre>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import dayjs from 'dayjs';

// 接口定义
interface ComposeItem {
  name: string;
  status: string;
  created: string;
  compose: string;
  envs: Array<{ key: string; value: string }>;
}

// 状态
const loading = ref(false);
const composeList = ref<ComposeItem[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
});

// 对话框状态
const composeDialogVisible = ref(false);
const viewDialogVisible = ref(false);
const isEdit = ref(false);
const currentCompose = ref<ComposeItem>({} as ComposeItem);

// 表单数据
const formData = reactive({
  name: '',
  compose: '',
  envs: [] as Array<{ key: string; value: string }>,
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入编排名称', type: 'error' }],
  compose: [{ required: true, message: '请输入 Docker Compose 内容', type: 'error' }],
};

// 表格列定义
const columns = [
  { colKey: 'name', title: '编排名称', width: 200 },
  { colKey: 'status', title: '状态', width: 120, cell: 'status' },
  { colKey: 'created', title: '创建时间', width: 180, cell: 'created' },
  { colKey: 'operation', title: '操作', width: 200, fixed: 'right', cell: 'operation' },
];

// 获取编排列表
const fetchComposeList = async () => {
  loading.value = true;
  try {
    // 这里应该调用实际的API
    // const res = await composeApi.list(pagination.current, pagination.pageSize);
    // composeList.value = res.data || [];
    // pagination.total = res.total || 0;
    
    // 模拟数据
    composeList.value = [
      {
        name: 'wordpress',
        status: 'running',
        created: '2024-01-15T10:30:00Z',
        compose: 'version: "3.8"\nservices:\n  wordpress:\n    image: wordpress:latest\n    ports:\n      - "8080:80"',
        envs: [{ key: 'WORDPRESS_DB_HOST', value: 'db:3306' }],
      },
    ];
    pagination.total = composeList.value.length;
  } catch (error) {
    console.error('获取编排列表失败:', error);
    MessagePlugin.error('获取编排列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
  fetchComposeList();
};

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 获取状态主题
const getStatusTheme = (status: string) => {
  switch (status) {
    case 'running':
      return 'success';
    case 'stopped':
      return 'danger';
    default:
      return 'warning';
  }
};

// 添加环境变量
const addEnv = () => {
  formData.envs.push({ key: '', value: '' });
};

// 移除环境变量
const removeEnv = (index: number) => {
  formData.envs.splice(index, 1);
};

// 处理创建编排
const handleCreateCompose = () => {
  isEdit.value = false;
  formData.name = '';
  formData.compose = '';
  formData.envs = [];
  composeDialogVisible.value = true;
};

// 处理编辑编排
const handleEditCompose = (compose: ComposeItem) => {
  isEdit.value = true;
  formData.name = compose.name;
  formData.compose = compose.compose;
  formData.envs = [...compose.envs];
  composeDialogVisible.value = true;
};

// 确认保存编排
const confirmSaveCompose = async () => {
  try {
    if (isEdit.value) {
      // await composeApi.update(formData.name, formData);
      MessagePlugin.success('更新编排成功');
    } else {
      // await composeApi.create(formData);
      MessagePlugin.success('创建编排成功');
    }
    composeDialogVisible.value = false;
    fetchComposeList();
  } catch (error) {
    console.error('保存编排失败:', error);
    MessagePlugin.error('保存编排失败');
  }
};

// 处理查看编排
const handleViewCompose = (compose: ComposeItem) => {
  currentCompose.value = compose;
  viewDialogVisible.value = true;
};

// 处理启动编排
const handleStartCompose = async (compose: ComposeItem) => {
  try {
    // await composeApi.up(compose.name);
    MessagePlugin.success('启动编排成功');
    fetchComposeList();
  } catch (error) {
    console.error('启动编排失败:', error);
    MessagePlugin.error('启动编排失败');
  }
};

// 处理停止编排
const handleStopCompose = async (compose: ComposeItem) => {
  try {
    // await composeApi.down(compose.name);
    MessagePlugin.success('停止编排成功');
    fetchComposeList();
  } catch (error) {
    console.error('停止编排失败:', error);
    MessagePlugin.error('停止编排失败');
  }
};

// 处理删除编排
const handleRemoveCompose = (compose: ComposeItem) => {
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除编排 "${compose.name}" 吗？`,
    confirmBtn: {
      content: '删除',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        // await composeApi.remove(compose.name);
        MessagePlugin.success('删除编排成功');
        fetchComposeList();
      } catch (error) {
        console.error('删除编排失败:', error);
        MessagePlugin.error('删除编排失败');
      }
    },
  });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchComposeList();
});
</script>

<style lang="less" scoped>
.container-compose-container {
  .compose-content {
    margin-top: 16px;
    
    h4 {
      margin-bottom: 8px;
      color: #333;
    }
    
    pre {
      background-color: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}
</style>
