<template>
  <div class="container-network-container page-container">
    <t-card title="网络管理">
      <template #actions>
        <t-space>
          <t-button theme="primary" @click="handleCreateNetwork">
            <template #icon>
              <t-icon name="add" />
            </template>
            创建网络
          </t-button>
          <t-button theme="default" @click="fetchNetworks">
            <template #icon>
              <t-icon name="refresh" />
            </template>
            刷新
          </t-button>
        </t-space>
      </template>

      <t-table
        :data="networks"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
        stripe
        hover
        row-key="id"
      >
        <template #operation="{ row }">
          <t-space>
            <t-tooltip content="删除网络">
              <t-button variant="text" theme="danger" @click="handleRemoveNetwork(row)">
                <template #icon>
                  <t-icon name="delete" />
                </template>
              </t-button>
            </t-tooltip>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 创建网络对话框 -->
    <t-dialog
      v-model:visible="createDialogVisible"
      header="创建网络"
      :confirm-btn="{ content: '创建', theme: 'primary' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="confirmCreateNetwork"
    >
      <t-form ref="form" :data="formData" :rules="rules" label-width="100px">
        <t-form-item label="网络名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入网络名称" />
        </t-form-item>
        <t-form-item label="驱动" name="driver">
          <t-select v-model="formData.driver">
            <t-option value="bridge" label="bridge" />
            <t-option value="host" label="host" />
            <t-option value="none" label="none" />
            <t-option value="overlay" label="overlay" />
            <t-option value="macvlan" label="macvlan" />
            <t-option value="ipvlan" label="ipvlan" />
          </t-select>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import containerApi, { Network } from '@/api/modules/container';

// 状态
const loading = ref(false);
const networks = ref<Network[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
});

// 对话框状态
const createDialogVisible = ref(false);

// 表单数据
const formData = reactive({
  name: '',
  driver: 'bridge',
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入网络名称', type: 'error' }],
  driver: [{ required: true, message: '请选择驱动', type: 'error' }],
};

// 表格列定义
const columns = [
  { colKey: 'name', title: '名称', width: 200 },
  { colKey: 'id', title: 'ID', width: 300 },
  { colKey: 'driver', title: '驱动', width: 120 },
  { colKey: 'scope', title: '范围', width: 120 },
  { colKey: 'operation', title: '操作', width: 100, fixed: 'right', cell: 'operation' },
];

// 获取网络列表
const fetchNetworks = async () => {
  loading.value = true;
  try {
    const res = await containerApi.listNetworks();
    networks.value = res.data || [];
    pagination.total = networks.value.length;
  } catch (error) {
    console.error('获取网络列表失败:', error);
    MessagePlugin.error('获取网络列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
};

// 处理创建网络
const handleCreateNetwork = () => {
  formData.name = '';
  formData.driver = 'bridge';
  createDialogVisible.value = true;
};

// 确认创建网络
const confirmCreateNetwork = async () => {
  try {
    await containerApi.createNetwork(formData.name, formData.driver);
    MessagePlugin.success('创建网络成功');
    createDialogVisible.value = false;
    fetchNetworks();
  } catch (error) {
    console.error('创建网络失败:', error);
    MessagePlugin.error('创建网络失败');
  }
};

// 处理删除网络
const handleRemoveNetwork = (network: Network) => {
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除网络 "${network.name}" 吗？`,
    confirmBtn: {
      content: '删除',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        await containerApi.removeNetwork(network.id);
        MessagePlugin.success('删除网络成功');
        fetchNetworks();
      } catch (error) {
        console.error('删除网络失败:', error);
        MessagePlugin.error('删除网络失败');
      }
    },
  });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchNetworks();
});
</script>

<style lang="less" scoped>
.container-network-container {
  // 自定义样式
}
</style>
