<template>
  <div class="container-network-container page-container">
    <t-card title="网络管理">
      <template #actions>
        <t-space>
          <t-button theme="primary" @click="handleCreateNetwork">
            <template #icon>
              <t-icon name="add" />
            </template>
            创建网络
          </t-button>
          <t-button theme="default" @click="fetchNetworks">
            <template #icon>
              <t-icon name="refresh" />
            </template>
            刷新
          </t-button>
          <t-button theme="primary" variant="outline" @click="handlePrune">
            <template #icon>
              <t-icon name="delete" />
            </template>
            清理网络
          </t-button>
        </t-space>
      </template>

      <t-table
        :data="networks"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
        stripe
        hover
        row-key="id"
      >
        <template #subnet="{ row }">
          <div v-if="row.ipam && row.ipam.config && row.ipam.config.length > 0">
            <t-tag v-for="(config, index) in row.ipam.config" :key="index" variant="light">
              {{ config.subnet }}
            </t-tag>
          </div>
          <span v-else>-</span>
        </template>

        <template #operation="{ row }">
          <t-space>
            <t-tooltip content="查看详情">
              <t-button variant="text" theme="primary" @click="handleViewNetwork(row)">
                <template #icon>
                  <t-icon name="view-list" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip content="删除网络">
              <t-button variant="text" theme="danger" @click="handleRemoveNetwork(row)">
                <template #icon>
                  <t-icon name="delete" />
                </template>
              </t-button>
            </t-tooltip>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 创建网络对话框 -->
    <t-dialog
      v-model:visible="createDialogVisible"
      header="创建网络"
      :confirm-btn="{ content: '创建', theme: 'primary' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="confirmCreateNetwork"
      width="600px"
    >
      <t-form ref="form" :data="formData" :rules="rules" label-width="120px">
        <t-form-item label="网络名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入网络名称" />
        </t-form-item>
        <t-form-item label="驱动" name="driver">
          <t-select v-model="formData.driver">
            <t-option value="bridge" label="bridge" />
            <t-option value="host" label="host" />
            <t-option value="none" label="none" />
            <t-option value="overlay" label="overlay" />
            <t-option value="macvlan" label="macvlan" />
            <t-option value="ipvlan" label="ipvlan" />
          </t-select>
        </t-form-item>
        <t-form-item label="IPv4配置">
          <t-checkbox v-model="formData.ipv4.enabled">启用IPv4</t-checkbox>
        </t-form-item>
        <div v-if="formData.ipv4.enabled">
          <t-form-item label="子网" name="ipv4.subnet">
            <t-input v-model="formData.ipv4.subnet" placeholder="例如：***********/24" />
          </t-form-item>
          <t-form-item label="网关" name="ipv4.gateway">
            <t-input v-model="formData.ipv4.gateway" placeholder="例如：***********" />
          </t-form-item>
          <t-form-item label="IP范围" name="ipv4.ip_range">
            <t-input v-model="formData.ipv4.ip_range" placeholder="例如：***********/28" />
          </t-form-item>
        </div>
        <t-form-item label="标签">
          <t-space direction="vertical" style="width: 100%">
            <t-space v-for="(label, index) in formData.labels" :key="index" align="center">
              <t-input v-model="label.key" placeholder="标签名" style="width: 180px" />
              <span>=</span>
              <t-input v-model="label.value" placeholder="标签值" style="width: 250px" />
              <t-button theme="danger" variant="text" @click="removeLabel(index)">
                <template #icon>
                  <t-icon name="close" />
                </template>
              </t-button>
            </t-space>
            <t-button theme="primary" variant="text" @click="addLabel">
              <template #icon>
                <t-icon name="add" />
              </template>
              添加标签
            </t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 查看网络详情对话框 -->
    <t-dialog
      v-model:visible="viewDialogVisible"
      header="网络详情"
      :confirm-btn="{ content: '关闭', theme: 'primary' }"
      :cancel-btn="null"
      width="800px"
    >
      <t-descriptions bordered>
        <t-descriptions-item label="网络名称">{{ currentNetwork.name }}</t-descriptions-item>
        <t-descriptions-item label="网络ID">{{ currentNetwork.id }}</t-descriptions-item>
        <t-descriptions-item label="驱动">{{ currentNetwork.driver }}</t-descriptions-item>
        <t-descriptions-item label="范围">{{ currentNetwork.scope }}</t-descriptions-item>
        <t-descriptions-item label="创建时间">{{ formatDate(currentNetwork.created) }}</t-descriptions-item>
      </t-descriptions>

      <div v-if="currentNetwork.ipam && currentNetwork.ipam.config" class="network-config">
        <h4>网络配置：</h4>
        <t-table :data="currentNetwork.ipam.config" :columns="configColumns" size="small" />
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import containerApi, { Network } from '@/api/modules/container';

// 状态
const loading = ref(false);
const networks = ref<Network[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
});

// 对话框状态
const createDialogVisible = ref(false);
const viewDialogVisible = ref(false);
const currentNetwork = ref<Network>({} as Network);

// 表单数据
const formData = reactive({
  name: '',
  driver: 'bridge',
  ipv4: {
    enabled: false,
    subnet: '',
    gateway: '',
    ip_range: '',
  },
  labels: [] as Array<{ key: string; value: string }>,
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入网络名称', type: 'error' }],
  driver: [{ required: true, message: '请选择驱动', type: 'error' }],
};

// 表格列定义
const columns = [
  { colKey: 'name', title: '名称', width: 200 },
  { colKey: 'id', title: 'ID', width: 300 },
  { colKey: 'driver', title: '驱动', width: 120 },
  { colKey: 'scope', title: '范围', width: 120 },
  { colKey: 'subnet', title: '子网', cell: 'subnet' },
  { colKey: 'operation', title: '操作', width: 150, fixed: 'right', cell: 'operation' },
];

// 网络配置表格列定义
const configColumns = [
  { colKey: 'subnet', title: '子网' },
  { colKey: 'gateway', title: '网关' },
  { colKey: 'ip_range', title: 'IP范围' },
];

// 获取网络列表
const fetchNetworks = async () => {
  loading.value = true;
  try {
    const res = await containerApi.listNetworks();
    networks.value = res.data || [];
    pagination.total = networks.value.length;
  } catch (error) {
    console.error('获取网络列表失败:', error);
    MessagePlugin.error('获取网络列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
};

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-';
  return new Date(date).toLocaleString();
};

// 添加标签
const addLabel = () => {
  formData.labels.push({ key: '', value: '' });
};

// 移除标签
const removeLabel = (index: number) => {
  formData.labels.splice(index, 1);
};

// 处理创建网络
const handleCreateNetwork = () => {
  formData.name = '';
  formData.driver = 'bridge';
  formData.ipv4.enabled = false;
  formData.ipv4.subnet = '';
  formData.ipv4.gateway = '';
  formData.ipv4.ip_range = '';
  formData.labels = [];
  createDialogVisible.value = true;
};

// 确认创建网络
const confirmCreateNetwork = async () => {
  try {
    await containerApi.createNetwork(formData.name, formData.driver);
    MessagePlugin.success('创建网络成功');
    createDialogVisible.value = false;
    fetchNetworks();
  } catch (error) {
    console.error('创建网络失败:', error);
    MessagePlugin.error('创建网络失败');
  }
};

// 处理删除网络
const handleRemoveNetwork = (network: Network) => {
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除网络 "${network.name}" 吗？`,
    confirmBtn: {
      content: '删除',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        await containerApi.removeNetwork(network.id);
        MessagePlugin.success('删除网络成功');
        fetchNetworks();
      } catch (error) {
        console.error('删除网络失败:', error);
        MessagePlugin.error('删除网络失败');
      }
    },
  });
};

// 处理查看网络详情
const handleViewNetwork = (network: Network) => {
  currentNetwork.value = network;
  viewDialogVisible.value = true;
};

// 处理清理网络
const handlePrune = () => {
  DialogPlugin.confirm({
    header: '确认清理',
    body: '确定要清理所有未使用的网络吗？此操作不可逆。',
    confirmBtn: {
      content: '清理',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        // await containerApi.pruneNetworks();
        MessagePlugin.success('清理网络成功');
        fetchNetworks();
      } catch (error) {
        console.error('清理网络失败:', error);
        MessagePlugin.error('清理网络失败');
      }
    },
  });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchNetworks();
});
</script>

<style lang="less" scoped>
.container-network-container {
  .network-config {
    margin-top: 16px;

    h4 {
      margin-bottom: 8px;
      color: #333;
    }
  }
}
</style>
