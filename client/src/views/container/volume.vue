<template>
  <div class="container-volume-container page-container">
    <t-card title="数据卷管理">
      <template #actions>
        <t-space>
          <t-button theme="primary" @click="handleCreateVolume">
            <template #icon>
              <t-icon name="add" />
            </template>
            创建数据卷
          </t-button>
          <t-button theme="default" @click="fetchVolumes">
            <template #icon>
              <t-icon name="refresh" />
            </template>
            刷新
          </t-button>
        </t-space>
      </template>

      <t-table
        :data="volumes"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
        stripe
        hover
        row-key="name"
      >
        <template #created_at="{ row }">
          {{ formatDate(row.created_at) }}
        </template>

        <template #operation="{ row }">
          <t-space>
            <t-tooltip content="删除数据卷">
              <t-button variant="text" theme="danger" @click="handleRemoveVolume(row)">
                <template #icon>
                  <t-icon name="delete" />
                </template>
              </t-button>
            </t-tooltip>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 创建数据卷对话框 -->
    <t-dialog
      v-model:visible="createDialogVisible"
      header="创建数据卷"
      :confirm-btn="{ content: '创建', theme: 'primary' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="confirmCreateVolume"
    >
      <t-form ref="form" :data="formData" :rules="rules" label-width="100px">
        <t-form-item label="数据卷名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入数据卷名称" />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import containerApi, { Volume } from '@/api/modules/container';
import dayjs from 'dayjs';

// 状态
const loading = ref(false);
const volumes = ref<Volume[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
});

// 对话框状态
const createDialogVisible = ref(false);

// 表单数据
const formData = reactive({
  name: '',
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入数据卷名称', type: 'error' }],
};

// 表格列定义
const columns = [
  { colKey: 'name', title: '名称', width: 200 },
  { colKey: 'driver', title: '驱动', width: 120 },
  { colKey: 'mountpoint', title: '挂载点', width: 300 },
  { colKey: 'created_at', title: '创建时间', width: 180, cell: 'created_at' },
  { colKey: 'operation', title: '操作', width: 100, fixed: 'right', cell: 'operation' },
];

// 获取数据卷列表
const fetchVolumes = async () => {
  loading.value = true;
  try {
    const res = await containerApi.listVolumes();
    volumes.value = res.data || [];
    pagination.total = volumes.value.length;
  } catch (error) {
    console.error('获取数据卷列表失败:', error);
    MessagePlugin.error('获取数据卷列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
};

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 处理创建数据卷
const handleCreateVolume = () => {
  formData.name = '';
  createDialogVisible.value = true;
};

// 确认创建数据卷
const confirmCreateVolume = async () => {
  try {
    await containerApi.createVolume(formData.name);
    MessagePlugin.success('创建数据卷成功');
    createDialogVisible.value = false;
    fetchVolumes();
  } catch (error) {
    console.error('创建数据卷失败:', error);
    MessagePlugin.error('创建数据卷失败');
  }
};

// 处理删除数据卷
const handleRemoveVolume = (volume: Volume) => {
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除数据卷 "${volume.name}" 吗？`,
    confirmBtn: {
      content: '删除',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        await containerApi.removeVolume(volume.name);
        MessagePlugin.success('删除数据卷成功');
        fetchVolumes();
      } catch (error) {
        console.error('删除数据卷失败:', error);
        MessagePlugin.error('删除数据卷失败');
      }
    },
  });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchVolumes();
});
</script>

<style lang="less" scoped>
.container-volume-container {
  // 自定义样式
}
</style>
