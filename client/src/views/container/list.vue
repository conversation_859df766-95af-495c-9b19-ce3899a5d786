<template>
  <div class="container-list-container page-container">
    <t-card title="容器列表">
      <template #actions>
        <t-space>
          <t-checkbox v-model="showAll">显示所有容器</t-checkbox>
          <t-button theme="primary" @click="handleCreateContainer">
            <template #icon>
              <t-icon name="add" />
            </template>
            创建容器
          </t-button>
          <t-button theme="default" @click="fetchContainers">
            <template #icon>
              <t-icon name="refresh" />
            </template>
            刷新
          </t-button>
          <t-button theme="primary" variant="outline" @click="handlePrune">
            <template #icon>
              <t-icon name="delete" />
            </template>
            清理容器
          </t-button>
        </t-space>
      </template>

      <!-- 批量操作按钮组 -->
      <div v-if="selectedRowKeys.length > 0" class="bulk-actions">
        <t-space>
          <span>已选择 {{ selectedRowKeys.length }} 个容器</span>
          <t-button theme="success" size="small" @click="bulkStart">批量启动</t-button>
          <t-button theme="warning" size="small" @click="bulkStop">批量停止</t-button>
          <t-button theme="default" size="small" @click="bulkRestart">批量重启</t-button>
          <t-button theme="warning" size="small" @click="bulkPause">批量暂停</t-button>
          <t-button theme="success" size="small" @click="bulkUnpause">批量恢复</t-button>
          <t-button theme="danger" size="small" @click="bulkDelete">批量删除</t-button>
        </t-space>
      </div>

      <t-table
        :data="containers"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
        @select-change="onSelectChange"
        stripe
        hover
        row-key="id"
        :selected-row-keys="selectedRowKeys"
      >
        <template #status="{ row }">
          <t-tag :theme="getStatusTheme(row.status)" variant="light">
            {{ row.status }}
          </t-tag>
        </template>

        <template #ports="{ row }">
          <div v-if="row.ports && row.ports.length > 0">
            <div v-for="(port, index) in row.ports" :key="index">
              {{ port.ip || '0.0.0.0' }}:{{ port.public_port }} -> {{ port.private_port }}/{{ port.type }}
            </div>
          </div>
          <span v-else>-</span>
        </template>

        <template #created="{ row }">
          {{ formatDate(row.created) }}
        </template>

        <template #operation="{ row }">
          <t-space>
            <t-tooltip content="查看日志">
              <t-button variant="text" theme="primary" @click="handleViewLogs(row)">
                <template #icon>
                  <t-icon name="file-copy" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip content="查看统计">
              <t-button variant="text" theme="primary" @click="handleViewStats(row)">
                <template #icon>
                  <t-icon name="chart" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip content="重命名">
              <t-button variant="text" theme="default" @click="handleRenameContainer(row)">
                <template #icon>
                  <t-icon name="edit" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip :content="row.is_running ? '停止' : '启动'">
              <t-button
                variant="text"
                :theme="row.is_running ? 'danger' : 'success'"
                @click="row.is_running ? handleStopContainer(row) : handleStartContainer(row)"
              >
                <template #icon>
                  <t-icon :name="row.is_running ? 'stop-circle' : 'play-circle'" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip content="重启">
              <t-button variant="text" theme="warning" @click="handleRestartContainer(row)">
                <template #icon>
                  <t-icon name="refresh" />
                </template>
              </t-button>
            </t-tooltip>

            <t-dropdown :options="getMoreActions(row)" @click="handleMoreAction">
              <t-button variant="text" theme="default">
                <template #icon>
                  <t-icon name="more" />
                </template>
              </t-button>
            </t-dropdown>

            <t-tooltip content="删除">
              <t-button variant="text" theme="danger" @click="handleRemoveContainer(row)">
                <template #icon>
                  <t-icon name="delete" />
                </template>
              </t-button>
            </t-tooltip>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 创建容器组件 -->
    <ContainerCreate v-model:visible="createDialogVisible" @success="handleCreateSuccess" />

    <!-- 日志对话框 -->
    <t-dialog
      v-model:visible="logsDialogVisible"
      header="容器日志"
      :confirm-btn="{ content: '关闭', theme: 'primary' }"
      :cancel-btn="null"
      width="800px"
    >
      <div class="logs-container">
        <pre>{{ containerLogs }}</pre>
      </div>
    </t-dialog>

    <!-- 统计信息对话框 -->
    <t-dialog
      v-model:visible="statsDialogVisible"
      header="容器统计信息"
      :confirm-btn="{ content: '关闭', theme: 'primary' }"
      :cancel-btn="null"
      width="800px"
    >
      <t-loading :loading="statsLoading">
        <t-descriptions bordered>
          <t-descriptions-item label="CPU使用率">{{ containerStats.CPUPerc || '-' }}</t-descriptions-item>
          <t-descriptions-item label="内存使用">{{ containerStats.MemUsage || '-' }}</t-descriptions-item>
          <t-descriptions-item label="内存使用率">{{ containerStats.MemPerc || '-' }}</t-descriptions-item>
          <t-descriptions-item label="网络I/O">{{ containerStats.NetIO || '-' }}</t-descriptions-item>
          <t-descriptions-item label="块I/O">{{ containerStats.BlockIO || '-' }}</t-descriptions-item>
          <t-descriptions-item label="进程数">{{ containerStats.PIDs || '-' }}</t-descriptions-item>
        </t-descriptions>
      </t-loading>
    </t-dialog>

    <!-- 重命名对话框 -->
    <t-dialog
      v-model:visible="renameDialogVisible"
      header="重命名容器"
      :confirm-btn="{ content: '确认', theme: 'primary' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="confirmRename"
      width="500px"
    >
      <t-form ref="renameForm" :data="renameFormData" :rules="renameRules" label-width="100px">
        <t-form-item label="新名称" name="name">
          <t-input v-model="renameFormData.name" placeholder="请输入新的容器名称" />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import containerApi, { Container, Port } from '@/api/modules/container';
import ContainerCreate from './create.vue';
import dayjs from 'dayjs';

// 状态
const loading = ref(false);
const containers = ref<Container[]>([]);
const showAll = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
});

// 对话框状态
const createDialogVisible = ref(false);
const logsDialogVisible = ref(false);
const statsDialogVisible = ref(false);
const renameDialogVisible = ref(false);
const containerLogs = ref('');
const containerStats = ref<any>({});
const statsLoading = ref(false);

// 选中的行
const selectedRowKeys = ref<string[]>([]);

// 重命名表单
const renameFormData = reactive({
  id: '',
  name: '',
});

const renameRules = {
  name: [{ required: true, message: '请输入容器名称', type: 'error' }],
};

// 这些表单相关的代码已经移到 ContainerCreate 组件中

// 表格列定义
const columns = [
  { colKey: 'serial-number', type: 'multiple-select', width: 50 },
  { colKey: 'name', title: '名称', width: 180 },
  { colKey: 'image', title: '镜像', width: 200 },
  { colKey: 'status', title: '状态', width: 120, cell: 'status' },
  { colKey: 'ports', title: '端口映射', cell: 'ports' },
  { colKey: 'created', title: '创建时间', width: 180, cell: 'created' },
  { colKey: 'operation', title: '操作', width: 280, fixed: 'right', cell: 'operation' },
];

// 监听显示所有容器的变化
watch(showAll, () => {
  fetchContainers();
});

// 获取容器列表
const fetchContainers = async () => {
  loading.value = true;
  try {
    const res = await containerApi.list(showAll.value);
    containers.value = res.data || [];
    pagination.total = containers.value.length;
  } catch (error) {
    console.error('获取容器列表失败:', error);
    MessagePlugin.error('获取容器列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
};

// 选择变化
const onSelectChange = (value: string[]) => {
  selectedRowKeys.value = value;
};

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 获取状态主题
const getStatusTheme = (status: string) => {
  if (status.includes('Up') || status.includes('running')) {
    return 'success';
  } else if (status.includes('Exited') || status.includes('stopped')) {
    return 'danger';
  } else {
    return 'warning';
  }
};

// 处理创建容器
const handleCreateContainer = () => {
  createDialogVisible.value = true;
};

// 创建容器成功回调
const handleCreateSuccess = () => {
  fetchContainers();
};

// 处理启动容器
const handleStartContainer = async (container: Container) => {
  try {
    await containerApi.start(container.id);
    MessagePlugin.success('启动容器成功');
    fetchContainers();
  } catch (error) {
    console.error('启动容器失败:', error);
    MessagePlugin.error('启动容器失败');
  }
};

// 处理停止容器
const handleStopContainer = async (container: Container) => {
  try {
    await containerApi.stop(container.id);
    MessagePlugin.success('停止容器成功');
    fetchContainers();
  } catch (error) {
    console.error('停止容器失败:', error);
    MessagePlugin.error('停止容器失败');
  }
};

// 处理重启容器
const handleRestartContainer = async (container: Container) => {
  try {
    await containerApi.restart(container.id);
    MessagePlugin.success('重启容器成功');
    fetchContainers();
  } catch (error) {
    console.error('重启容器失败:', error);
    MessagePlugin.error('重启容器失败');
  }
};

// 处理删除容器
const handleRemoveContainer = (container: Container) => {
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除容器 "${container.name}" 吗？`,
    confirmBtn: {
      content: '删除',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        await containerApi.remove(container.id, true);
        MessagePlugin.success('删除容器成功');
        fetchContainers();
      } catch (error) {
        console.error('删除容器失败:', error);
        MessagePlugin.error('删除容器失败');
      }
    },
  });
};

// 处理查看日志
const handleViewLogs = async (container: Container) => {
  try {
    const res = await containerApi.logs(container.id);
    containerLogs.value = res.data.logs || '无日志';
    logsDialogVisible.value = true;
  } catch (error) {
    console.error('获取容器日志失败:', error);
    MessagePlugin.error('获取容器日志失败');
  }
};

// 处理查看统计信息
const handleViewStats = async (container: Container) => {
  statsLoading.value = true;
  statsDialogVisible.value = true;

  try {
    const res = await containerApi.stats(container.id);
    containerStats.value = res.data || {};
  } catch (error) {
    console.error('获取容器统计信息失败:', error);
    MessagePlugin.error('获取容器统计信息失败');
  } finally {
    statsLoading.value = false;
  }
};

// 处理重命名容器
const handleRenameContainer = (container: Container) => {
  renameFormData.id = container.id;
  renameFormData.name = container.name;
  renameDialogVisible.value = true;
};

// 确认重命名
const confirmRename = async () => {
  try {
    await containerApi.rename(renameFormData.id, renameFormData.name);
    MessagePlugin.success('重命名容器成功');
    renameDialogVisible.value = false;
    fetchContainers();
  } catch (error) {
    console.error('重命名容器失败:', error);
    MessagePlugin.error('重命名容器失败');
  }
};

// 获取更多操作选项
const getMoreActions = (row: Container) => {
  return [
    {
      content: row.is_running ? '暂停' : '恢复',
      value: row.is_running ? 'pause' : 'unpause',
      onClick: () => row.is_running ? handlePauseContainer(row) : handleUnpauseContainer(row),
    },
    {
      content: '强制停止',
      value: 'kill',
      onClick: () => handleKillContainer(row),
    },
  ];
};

// 处理更多操作
const handleMoreAction = (data: any) => {
  // 这里可以处理更多操作的逻辑
};

// 处理暂停容器
const handlePauseContainer = async (container: Container) => {
  try {
    await containerApi.pause(container.id);
    MessagePlugin.success('暂停容器成功');
    fetchContainers();
  } catch (error) {
    console.error('暂停容器失败:', error);
    MessagePlugin.error('暂停容器失败');
  }
};

// 处理恢复容器
const handleUnpauseContainer = async (container: Container) => {
  try {
    await containerApi.unpause(container.id);
    MessagePlugin.success('恢复容器成功');
    fetchContainers();
  } catch (error) {
    console.error('恢复容器失败:', error);
    MessagePlugin.error('恢复容器失败');
  }
};

// 处理强制停止容器
const handleKillContainer = async (container: Container) => {
  try {
    await containerApi.kill(container.id);
    MessagePlugin.success('强制停止容器成功');
    fetchContainers();
  } catch (error) {
    console.error('强制停止容器失败:', error);
    MessagePlugin.error('强制停止容器失败');
  }
};

// 处理清理容器
const handlePrune = () => {
  DialogPlugin.confirm({
    header: '确认清理',
    body: '确定要清理所有已停止的容器吗？此操作不可逆。',
    confirmBtn: {
      content: '清理',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        // await containerApi.prune();
        MessagePlugin.success('清理容器成功');
        fetchContainers();
      } catch (error) {
        console.error('清理容器失败:', error);
        MessagePlugin.error('清理容器失败');
      }
    },
  });
};

// 批量启动
const bulkStart = async () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请先选择要操作的容器');
    return;
  }

  try {
    for (const id of selectedRowKeys.value) {
      await containerApi.start(id);
    }
    MessagePlugin.success('批量启动容器成功');
    selectedRowKeys.value = [];
    fetchContainers();
  } catch (error) {
    console.error('批量启动容器失败:', error);
    MessagePlugin.error('批量启动容器失败');
  }
};

// 批量停止
const bulkStop = async () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请先选择要操作的容器');
    return;
  }

  try {
    for (const id of selectedRowKeys.value) {
      await containerApi.stop(id);
    }
    MessagePlugin.success('批量停止容器成功');
    selectedRowKeys.value = [];
    fetchContainers();
  } catch (error) {
    console.error('批量停止容器失败:', error);
    MessagePlugin.error('批量停止容器失败');
  }
};

// 批量重启
const bulkRestart = async () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请先选择要操作的容器');
    return;
  }

  try {
    for (const id of selectedRowKeys.value) {
      await containerApi.restart(id);
    }
    MessagePlugin.success('批量重启容器成功');
    selectedRowKeys.value = [];
    fetchContainers();
  } catch (error) {
    console.error('批量重启容器失败:', error);
    MessagePlugin.error('批量重启容器失败');
  }
};

// 批量暂停
const bulkPause = async () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请先选择要操作的容器');
    return;
  }

  try {
    for (const id of selectedRowKeys.value) {
      // await containerApi.pause(id);
    }
    MessagePlugin.success('批量暂停容器成功');
    selectedRowKeys.value = [];
    fetchContainers();
  } catch (error) {
    console.error('批量暂停容器失败:', error);
    MessagePlugin.error('批量暂停容器失败');
  }
};

// 批量恢复
const bulkUnpause = async () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请先选择要操作的容器');
    return;
  }

  try {
    for (const id of selectedRowKeys.value) {
      // await containerApi.unpause(id);
    }
    MessagePlugin.success('批量恢复容器成功');
    selectedRowKeys.value = [];
    fetchContainers();
  } catch (error) {
    console.error('批量恢复容器失败:', error);
    MessagePlugin.error('批量恢复容器失败');
  }
};

// 批量删除
const bulkDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请先选择要操作的容器');
    return;
  }

  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除选中的 ${selectedRowKeys.value.length} 个容器吗？`,
    confirmBtn: {
      content: '删除',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        for (const id of selectedRowKeys.value) {
          await containerApi.remove(id, true);
        }
        MessagePlugin.success('批量删除容器成功');
        selectedRowKeys.value = [];
        fetchContainers();
      } catch (error) {
        console.error('批量删除容器失败:', error);
        MessagePlugin.error('批量删除容器失败');
      }
    },
  });
};

// 组件挂载时获取数据
onMounted(() => {
  fetchContainers();
});
</script>

<style lang="less" scoped>
.container-list-container {
  .bulk-actions {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    span {
      color: #495057;
      font-weight: 500;
    }
  }

  .logs-container {
    max-height: 400px;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 4px;

    pre {
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}
</style>
