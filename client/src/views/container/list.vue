<template>
  <div class="container-list-container page-container">
    <t-card title="容器列表">
      <template #actions>
        <t-space>
          <t-checkbox v-model="showAll">显示所有容器</t-checkbox>
          <t-button theme="primary" @click="handleCreateContainer">
            <template #icon>
              <t-icon name="add" />
            </template>
            创建容器
          </t-button>
          <t-button theme="default" @click="fetchContainers">
            <template #icon>
              <t-icon name="refresh" />
            </template>
            刷新
          </t-button>
        </t-space>
      </template>

      <t-table
        :data="containers"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @page-change="onPageChange"
        stripe
        hover
        row-key="id"
      >
        <template #status="{ row }">
          <t-tag :theme="getStatusTheme(row.status)" variant="light">
            {{ row.status }}
          </t-tag>
        </template>

        <template #ports="{ row }">
          <div v-if="row.ports && row.ports.length > 0">
            <div v-for="(port, index) in row.ports" :key="index">
              {{ port.ip || '0.0.0.0' }}:{{ port.public_port }} -> {{ port.private_port }}/{{ port.type }}
            </div>
          </div>
          <span v-else>-</span>
        </template>

        <template #created="{ row }">
          {{ formatDate(row.created) }}
        </template>

        <template #operation="{ row }">
          <t-space>
            <t-tooltip content="查看日志">
              <t-button variant="text" theme="primary" @click="handleViewLogs(row)">
                <template #icon>
                  <t-icon name="file-copy" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip content="查看统计">
              <t-button variant="text" theme="primary" @click="handleViewStats(row)">
                <template #icon>
                  <t-icon name="chart" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip :content="row.is_running ? '停止' : '启动'">
              <t-button
                variant="text"
                :theme="row.is_running ? 'danger' : 'success'"
                @click="row.is_running ? handleStopContainer(row) : handleStartContainer(row)"
              >
                <template #icon>
                  <t-icon :name="row.is_running ? 'stop-circle' : 'play-circle'" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip content="重启">
              <t-button variant="text" theme="warning" @click="handleRestartContainer(row)">
                <template #icon>
                  <t-icon name="refresh" />
                </template>
              </t-button>
            </t-tooltip>

            <t-tooltip content="删除">
              <t-button variant="text" theme="danger" @click="handleRemoveContainer(row)">
                <template #icon>
                  <t-icon name="delete" />
                </template>
              </t-button>
            </t-tooltip>
          </t-space>
        </template>
      </t-table>
    </t-card>

    <!-- 创建容器对话框 -->
    <t-dialog
      v-model:visible="createDialogVisible"
      header="创建容器"
      :confirm-btn="{ content: '创建', theme: 'primary' }"
      :cancel-btn="{ content: '取消' }"
      @confirm="confirmCreateContainer"
      width="700px"
    >
      <t-form ref="form" :data="formData" :rules="rules" label-width="120px">
        <t-form-item label="容器名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入容器名称" />
        </t-form-item>
        <t-form-item label="镜像" name="image">
          <t-input v-model="formData.image" placeholder="请输入镜像名称，例如：nginx:latest" />
        </t-form-item>
        <t-form-item label="命令" name="command">
          <t-input v-model="formData.command" placeholder="可选，容器启动命令" />
        </t-form-item>
        <t-form-item label="端口映射">
          <t-space direction="vertical" style="width: 100%">
            <t-space v-for="(port, index) in formData.ports" :key="index" align="center">
              <t-input v-model="port.ip" placeholder="IP (可选)" style="width: 120px" />
              <span>:</span>
              <t-input-number v-model="port.public_port" placeholder="外部端口" :min="1" :max="65535" style="width: 120px" />
              <span>-></span>
              <t-input-number v-model="port.private_port" placeholder="内部端口" :min="1" :max="65535" style="width: 120px" />
              <t-select v-model="port.type" style="width: 100px">
                <t-option value="tcp" label="TCP" />
                <t-option value="udp" label="UDP" />
              </t-select>
              <t-button theme="danger" variant="text" @click="removePort(index)">
                <template #icon>
                  <t-icon name="close" />
                </template>
              </t-button>
            </t-space>
            <t-button theme="primary" variant="text" @click="addPort">
              <template #icon>
                <t-icon name="add" />
              </template>
              添加端口映射
            </t-button>
          </t-space>
        </t-form-item>
        <t-form-item label="环境变量">
          <t-space direction="vertical" style="width: 100%">
            <t-space v-for="(value, key, index) in formData.env" :key="index" align="center">
              <t-input v-model="envKeys[index]" placeholder="变量名" style="width: 180px" />
              <span>=</span>
              <t-input v-model="envValues[index]" placeholder="变量值" style="width: 250px" />
              <t-button theme="danger" variant="text" @click="removeEnv(index)">
                <template #icon>
                  <t-icon name="close" />
                </template>
              </t-button>
            </t-space>
            <t-button theme="primary" variant="text" @click="addEnv">
              <template #icon>
                <t-icon name="add" />
              </template>
              添加环境变量
            </t-button>
          </t-space>
        </t-form-item>
        <t-form-item label="重启策略" name="restart_policy">
          <t-select v-model="formData.restart_policy">
            <t-option value="no" label="不自动重启" />
            <t-option value="always" label="总是重启" />
            <t-option value="on-failure" label="失败时重启" />
            <t-option value="unless-stopped" label="除非手动停止" />
          </t-select>
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 日志对话框 -->
    <t-dialog
      v-model:visible="logsDialogVisible"
      header="容器日志"
      :confirm-btn="{ content: '关闭', theme: 'primary' }"
      :cancel-btn="null"
      width="800px"
    >
      <div class="logs-container">
        <pre>{{ containerLogs }}</pre>
      </div>
    </t-dialog>

    <!-- 统计信息对话框 -->
    <t-dialog
      v-model:visible="statsDialogVisible"
      header="容器统计信息"
      :confirm-btn="{ content: '关闭', theme: 'primary' }"
      :cancel-btn="null"
      width="800px"
    >
      <t-loading :loading="statsLoading">
        <t-descriptions bordered>
          <t-descriptions-item label="CPU使用率">{{ containerStats.CPUPerc || '-' }}</t-descriptions-item>
          <t-descriptions-item label="内存使用">{{ containerStats.MemUsage || '-' }}</t-descriptions-item>
          <t-descriptions-item label="内存使用率">{{ containerStats.MemPerc || '-' }}</t-descriptions-item>
          <t-descriptions-item label="网络I/O">{{ containerStats.NetIO || '-' }}</t-descriptions-item>
          <t-descriptions-item label="块I/O">{{ containerStats.BlockIO || '-' }}</t-descriptions-item>
          <t-descriptions-item label="进程数">{{ containerStats.PIDs || '-' }}</t-descriptions-item>
        </t-descriptions>
      </t-loading>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import containerApi, { Container, Port } from '@/api/modules/container';
import dayjs from 'dayjs';

// 状态
const loading = ref(false);
const containers = ref<Container[]>([]);
const showAll = ref(false);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
});

// 对话框状态
const createDialogVisible = ref(false);
const logsDialogVisible = ref(false);
const statsDialogVisible = ref(false);
const containerLogs = ref('');
const containerStats = ref<any>({});
const statsLoading = ref(false);

// 表单数据
const formData = reactive({
  name: '',
  image: '',
  command: '',
  ports: [] as Port[],
  env: {} as Record<string, string>,
  restart_policy: 'unless-stopped',
});

// 环境变量临时存储
const envKeys = ref<string[]>([]);
const envValues = ref<string[]>([]);

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入容器名称', type: 'error' }],
  image: [{ required: true, message: '请输入镜像名称', type: 'error' }],
};

// 表格列定义
const columns = [
  { colKey: 'name', title: '名称', width: 180 },
  { colKey: 'image', title: '镜像', width: 200 },
  { colKey: 'status', title: '状态', width: 120, cell: 'status' },
  { colKey: 'ports', title: '端口映射', cell: 'ports' },
  { colKey: 'created', title: '创建时间', width: 180, cell: 'created' },
  { colKey: 'operation', title: '操作', width: 200, fixed: 'right', cell: 'operation' },
];

// 监听显示所有容器的变化
watch(showAll, () => {
  fetchContainers();
});

// 获取容器列表
const fetchContainers = async () => {
  loading.value = true;
  try {
    const res = await containerApi.list(showAll.value);
    containers.value = res.data || [];
    pagination.total = containers.value.length;
  } catch (error) {
    console.error('获取容器列表失败:', error);
    MessagePlugin.error('获取容器列表失败');
  } finally {
    loading.value = false;
  }
};

// 分页变化
const onPageChange = (pageInfo: { current: number; pageSize: number }) => {
  pagination.current = pageInfo.current;
  pagination.pageSize = pageInfo.pageSize;
};

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 获取状态主题
const getStatusTheme = (status: string) => {
  if (status.includes('Up') || status.includes('running')) {
    return 'success';
  } else if (status.includes('Exited') || status.includes('stopped')) {
    return 'danger';
  } else {
    return 'warning';
  }
};

// 添加端口映射
const addPort = () => {
  formData.ports.push({
    ip: '',
    private_port: 0,
    public_port: 0,
    type: 'tcp',
  });
};

// 移除端口映射
const removePort = (index: number) => {
  formData.ports.splice(index, 1);
};

// 添加环境变量
const addEnv = () => {
  envKeys.value.push('');
  envValues.value.push('');
};

// 移除环境变量
const removeEnv = (index: number) => {
  envKeys.value.splice(index, 1);
  envValues.value.splice(index, 1);
};

// 处理创建容器
const handleCreateContainer = () => {
  // 重置表单
  formData.name = '';
  formData.image = '';
  formData.command = '';
  formData.ports = [];
  formData.env = {};
  formData.restart_policy = 'unless-stopped';
  envKeys.value = [];
  envValues.value = [];
  
  createDialogVisible.value = true;
};

// 确认创建容器
const confirmCreateContainer = async () => {
  // 处理环境变量
  formData.env = {};
  envKeys.value.forEach((key, index) => {
    if (key && envValues.value[index]) {
      formData.env[key] = envValues.value[index];
    }
  });

  try {
    await containerApi.create(formData);
    MessagePlugin.success('创建容器成功');
    createDialogVisible.value = false;
    fetchContainers();
  } catch (error) {
    console.error('创建容器失败:', error);
    MessagePlugin.error('创建容器失败');
  }
};

// 处理启动容器
const handleStartContainer = async (container: Container) => {
  try {
    await containerApi.start(container.id);
    MessagePlugin.success('启动容器成功');
    fetchContainers();
  } catch (error) {
    console.error('启动容器失败:', error);
    MessagePlugin.error('启动容器失败');
  }
};

// 处理停止容器
const handleStopContainer = async (container: Container) => {
  try {
    await containerApi.stop(container.id);
    MessagePlugin.success('停止容器成功');
    fetchContainers();
  } catch (error) {
    console.error('停止容器失败:', error);
    MessagePlugin.error('停止容器失败');
  }
};

// 处理重启容器
const handleRestartContainer = async (container: Container) => {
  try {
    await containerApi.restart(container.id);
    MessagePlugin.success('重启容器成功');
    fetchContainers();
  } catch (error) {
    console.error('重启容器失败:', error);
    MessagePlugin.error('重启容器失败');
  }
};

// 处理删除容器
const handleRemoveContainer = (container: Container) => {
  DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除容器 "${container.name}" 吗？`,
    confirmBtn: {
      content: '删除',
      theme: 'danger',
    },
    onConfirm: async () => {
      try {
        await containerApi.remove(container.id, true);
        MessagePlugin.success('删除容器成功');
        fetchContainers();
      } catch (error) {
        console.error('删除容器失败:', error);
        MessagePlugin.error('删除容器失败');
      }
    },
  });
};

// 处理查看日志
const handleViewLogs = async (container: Container) => {
  try {
    const res = await containerApi.logs(container.id);
    containerLogs.value = res.data.logs || '无日志';
    logsDialogVisible.value = true;
  } catch (error) {
    console.error('获取容器日志失败:', error);
    MessagePlugin.error('获取容器日志失败');
  }
};

// 处理查看统计信息
const handleViewStats = async (container: Container) => {
  statsLoading.value = true;
  statsDialogVisible.value = true;
  
  try {
    const res = await containerApi.stats(container.id);
    containerStats.value = res.data || {};
  } catch (error) {
    console.error('获取容器统计信息失败:', error);
    MessagePlugin.error('获取容器统计信息失败');
  } finally {
    statsLoading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchContainers();
});
</script>

<style lang="less" scoped>
.container-list-container {
  .logs-container {
    max-height: 400px;
    overflow-y: auto;
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    
    pre {
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}
</style>
